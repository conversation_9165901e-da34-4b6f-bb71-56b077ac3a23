'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ZoomIn, ZoomOut, X, Maximize2, Minimize2 } from 'lucide-react';
import * as Tooltip from '@radix-ui/react-tooltip';
import MLComponentDetail from './MLComponentDetail';

interface ComponentTranslation {
  name: string;
  description: string;
  icon: string;
  features: string[];
  benefits: string[];
  technologies: string[];
}

interface InteractiveMLArchitectureTranslations {
  title: string;
  subtitle: string;
  phases: {
    dataIngestion: string;
    modelTraining: string;
    deployment: string;
    monitoring: string;
  };
  viewModes: {
    conceptual: string;
    implementation: string;
  };
  components: {
    // Data Ingestion components
    dataCollector: string;
    dataPreprocessor: string;
    featureSelector: string;
    dataQualityMonitor: string;
    // Model Training components
    modelBuilder: string;
    modelTrainer: string;
    modelEvaluator: string;
    automaticModelSelector: string;
    // Deployment components
    modelDeployer: string;
    modelPredictor: string;
    kubernetesCluster: string;
    forecastService: string;
    // Monitoring components
    alertProcessor: string;
    predictionsMonitor: string;
    notificationService: string;
    retrainingTrigger: string;
    // Detail modal keys
    keyFeatures: string;
    benefits: string;
    implementationTech: string;
    technicalConsiderations: string;
    componentWorkflow: string;
    implementationArchitecture: string;
    comingSoon: string;
    learnMore: string;
  };
  buttons: {
    zoomIn: string;
    zoomOut: string;
    fullscreen: string;
    close: string;
    runData: string;
    reset: string;
  };
  interactive?: {
    components?: {
      dataCollector?: ComponentTranslation;
      dataPreprocessor?: ComponentTranslation;
      featureSelector?: ComponentTranslation;
      dataQualityMonitor?: ComponentTranslation;
      modelBuilder?: ComponentTranslation;
      modelTrainer?: ComponentTranslation;
      modelEvaluator?: ComponentTranslation;
      automaticModelSelector?: ComponentTranslation;
      modelDeployer?: ComponentTranslation;
      modelPredictor?: ComponentTranslation;
      kubernetesCluster?: ComponentTranslation;
      forecastService?: ComponentTranslation;
      alertProcessor?: ComponentTranslation;
      predictionsMonitor?: ComponentTranslation;
      notificationService?: ComponentTranslation;
      retrainingTrigger?: ComponentTranslation;
      technologiesLabel?: string;
    };
    connections?: {
      rawData?: string;
      cleanedData?: string;
      selectedFeatures?: string;
      validatedData?: string;
      modelArchitecture?: string;
      trainedModels?: string;
      evaluationMetrics?: string;
      selectedModel?: string;
      deployedModel?: string;
      predictionService?: string;
      infrastructure?: string;
      forecasts?: string;
      performanceMetrics?: string;
      alerts?: string;
      notifications?: string;
      retrainingSignal?: string;
      dataQualityAlerts?: string;
      predictions?: string;
      dataRefreshSignal?: string;
    };
  };
}

interface InteractiveMLArchitectureProps {
  isOpen: boolean;
  onClose: () => void;
  translations: InteractiveMLArchitectureTranslations;
}

// Define component and connection types
interface ArchComponent {
  id: string;
  name: string;
  description: string;
  phase: 'dataIngestion' | 'modelTraining' | 'deployment' | 'monitoring';
  x: number; // Percentage position
  y: number; // Percentage position
  icon?: string;
  color?: string;
  features?: string[];
  benefits?: string[];
  technologies?: string[]; // For implementation view
}

interface Connection {
  source: string;
  target: string;
  dataType?: string; // What kind of data flows through this connection
  animated?: boolean;
}



const InteractiveMLArchitecture: React.FC<InteractiveMLArchitectureProps> = ({
  isOpen,
  onClose,
  translations
}) => {
  // State management
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [viewMode, setViewMode] = useState<'conceptual' | 'implementation'>('conceptual');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Create component data using translations
  const mlComponents: ArchComponent[] = [
    // Data Ingestion Phase
    {
      id: 'dataCollector',
      name: translations.interactive?.components?.dataCollector?.name || 'Data Collector',
      description: translations.interactive?.components?.dataCollector?.description || '',
      phase: 'dataIngestion',
      x: 20,
      y: 20,
      icon: translations.interactive?.components?.dataCollector?.icon || '📊',
      color: '#3B82F6',
      features: translations.interactive?.components?.dataCollector?.features || [],
      benefits: translations.interactive?.components?.dataCollector?.benefits || [],
      technologies: translations.interactive?.components?.dataCollector?.technologies || []
    },
    {
      id: 'dataPreprocessor',
      name: translations.interactive?.components?.dataPreprocessor?.name || 'Data Preprocessor',
      description: translations.interactive?.components?.dataPreprocessor?.description || '',
      phase: 'dataIngestion',
      x: 35,
      y: 20,
      icon: translations.interactive?.components?.dataPreprocessor?.icon || '🧹',
      color: '#3B82F6',
      features: translations.interactive?.components?.dataPreprocessor?.features || [],
      benefits: translations.interactive?.components?.dataPreprocessor?.benefits || [],
      technologies: translations.interactive?.components?.dataPreprocessor?.technologies || []
    },
    {
      id: 'featureSelector',
      name: translations.interactive?.components?.featureSelector?.name || 'Feature Selector',
      description: translations.interactive?.components?.featureSelector?.description || '',
      phase: 'dataIngestion',
      x: 50,
      y: 20,
      icon: translations.interactive?.components?.featureSelector?.icon || '🔍',
      color: '#3B82F6',
      features: translations.interactive?.components?.featureSelector?.features || [],
      benefits: translations.interactive?.components?.featureSelector?.benefits || [],
      technologies: translations.interactive?.components?.featureSelector?.technologies || []
    },
    {
      id: 'dataQualityMonitor',
      name: translations.interactive?.components?.dataQualityMonitor?.name || 'Data Quality Monitor',
      description: translations.interactive?.components?.dataQualityMonitor?.description || '',
      phase: 'dataIngestion',
      x: 65,
      y: 20,
      icon: translations.interactive?.components?.dataQualityMonitor?.icon || '📈',
      color: '#3B82F6',
      features: translations.interactive?.components?.dataQualityMonitor?.features || [],
      benefits: translations.interactive?.components?.dataQualityMonitor?.benefits || [],
      technologies: translations.interactive?.components?.dataQualityMonitor?.technologies || []
    },
    // Model Training Phase
    {
      id: 'modelBuilder',
      name: translations.interactive?.components?.modelBuilder?.name || 'Model Builder',
      description: translations.interactive?.components?.modelBuilder?.description || '',
      phase: 'modelTraining',
      x: 20,
      y: 40,
      icon: translations.interactive?.components?.modelBuilder?.icon || '🏗️',
      color: '#8B5CF6',
      features: translations.interactive?.components?.modelBuilder?.features || [],
      benefits: translations.interactive?.components?.modelBuilder?.benefits || [],
      technologies: translations.interactive?.components?.modelBuilder?.technologies || []
    },
    {
      id: 'modelTrainer',
      name: translations.interactive?.components?.modelTrainer?.name || 'Model Trainer',
      description: translations.interactive?.components?.modelTrainer?.description || '',
      phase: 'modelTraining',
      x: 35,
      y: 40,
      icon: translations.interactive?.components?.modelTrainer?.icon || '🏋️',
      color: '#8B5CF6',
      features: translations.interactive?.components?.modelTrainer?.features || [],
      benefits: translations.interactive?.components?.modelTrainer?.benefits || [],
      technologies: translations.interactive?.components?.modelTrainer?.technologies || []
    },
    {
      id: 'modelEvaluator',
      name: translations.interactive?.components?.modelEvaluator?.name || 'Model Evaluator',
      description: translations.interactive?.components?.modelEvaluator?.description || '',
      phase: 'modelTraining',
      x: 50,
      y: 40,
      icon: translations.interactive?.components?.modelEvaluator?.icon || '📋',
      color: '#8B5CF6',
      features: translations.interactive?.components?.modelEvaluator?.features || [],
      benefits: translations.interactive?.components?.modelEvaluator?.benefits || [],
      technologies: translations.interactive?.components?.modelEvaluator?.technologies || []
    },
    {
      id: 'automaticModelSelector',
      name: translations.interactive?.components?.automaticModelSelector?.name || 'Automatic Model Selector',
      description: translations.interactive?.components?.automaticModelSelector?.description || '',
      phase: 'modelTraining',
      x: 65,
      y: 40,
      icon: translations.interactive?.components?.automaticModelSelector?.icon || '🏆',
      color: '#8B5CF6',
      features: translations.interactive?.components?.automaticModelSelector?.features || [],
      benefits: translations.interactive?.components?.automaticModelSelector?.benefits || [],
      technologies: translations.interactive?.components?.automaticModelSelector?.technologies || []
    },
    // Deployment Phase
    {
      id: 'modelDeployer',
      name: translations.interactive?.components?.modelDeployer?.name || 'Model Deployer',
      description: translations.interactive?.components?.modelDeployer?.description || '',
      phase: 'deployment',
      x: 20,
      y: 60,
      icon: translations.interactive?.components?.modelDeployer?.icon || '🚀',
      color: '#10B981',
      features: translations.interactive?.components?.modelDeployer?.features || [],
      benefits: translations.interactive?.components?.modelDeployer?.benefits || [],
      technologies: translations.interactive?.components?.modelDeployer?.technologies || []
    },
    {
      id: 'modelPredictor',
      name: translations.interactive?.components?.modelPredictor?.name || 'Model Predictor',
      description: translations.interactive?.components?.modelPredictor?.description || '',
      phase: 'deployment',
      x: 35,
      y: 60,
      icon: translations.interactive?.components?.modelPredictor?.icon || '🔮',
      color: '#10B981',
      features: translations.interactive?.components?.modelPredictor?.features || [],
      benefits: translations.interactive?.components?.modelPredictor?.benefits || [],
      technologies: translations.interactive?.components?.modelPredictor?.technologies || []
    },
    {
      id: 'kubernetesCluster',
      name: translations.interactive?.components?.kubernetesCluster?.name || 'Kubernetes Cluster',
      description: translations.interactive?.components?.kubernetesCluster?.description || '',
      phase: 'deployment',
      x: 50,
      y: 60,
      icon: translations.interactive?.components?.kubernetesCluster?.icon || '⚙️',
      color: '#10B981',
      features: translations.interactive?.components?.kubernetesCluster?.features || [],
      benefits: translations.interactive?.components?.kubernetesCluster?.benefits || [],
      technologies: translations.interactive?.components?.kubernetesCluster?.technologies || []
    },
    {
      id: 'forecastService',
      name: translations.interactive?.components?.forecastService?.name || 'Forecast Service',
      description: translations.interactive?.components?.forecastService?.description || '',
      phase: 'deployment',
      x: 65,
      y: 60,
      icon: translations.interactive?.components?.forecastService?.icon || '📆',
      color: '#10B981',
      features: translations.interactive?.components?.forecastService?.features || [],
      benefits: translations.interactive?.components?.forecastService?.benefits || [],
      technologies: translations.interactive?.components?.forecastService?.technologies || []
    },
    // Monitoring Phase
    {
      id: 'alertProcessor',
      name: translations.interactive?.components?.alertProcessor?.name || 'Alert Processor',
      description: translations.interactive?.components?.alertProcessor?.description || '',
      phase: 'monitoring',
      x: 20,
      y: 80,
      icon: translations.interactive?.components?.alertProcessor?.icon || '🚨',
      color: '#EC4899',
      features: translations.interactive?.components?.alertProcessor?.features || [],
      benefits: translations.interactive?.components?.alertProcessor?.benefits || [],
      technologies: translations.interactive?.components?.alertProcessor?.technologies || []
    },
    {
      id: 'predictionsMonitor',
      name: translations.interactive?.components?.predictionsMonitor?.name || 'Predictions Monitor',
      description: translations.interactive?.components?.predictionsMonitor?.description || '',
      phase: 'monitoring',
      x: 35,
      y: 80,
      icon: translations.interactive?.components?.predictionsMonitor?.icon || '👁️',
      color: '#EC4899',
      features: translations.interactive?.components?.predictionsMonitor?.features || [],
      benefits: translations.interactive?.components?.predictionsMonitor?.benefits || [],
      technologies: translations.interactive?.components?.predictionsMonitor?.technologies || []
    },
    {
      id: 'notificationService',
      name: translations.interactive?.components?.notificationService?.name || 'Notification Service',
      description: translations.interactive?.components?.notificationService?.description || '',
      phase: 'monitoring',
      x: 50,
      y: 80,
      icon: translations.interactive?.components?.notificationService?.icon || '📱',
      color: '#EC4899',
      features: translations.interactive?.components?.notificationService?.features || [],
      benefits: translations.interactive?.components?.notificationService?.benefits || [],
      technologies: translations.interactive?.components?.notificationService?.technologies || []
    },
    {
      id: 'retrainingTrigger',
      name: translations.interactive?.components?.retrainingTrigger?.name || 'Retraining Trigger',
      description: translations.interactive?.components?.retrainingTrigger?.description || '',
      phase: 'monitoring',
      x: 65,
      y: 80,
      icon: translations.interactive?.components?.retrainingTrigger?.icon || '🔄',
      color: '#EC4899',
      features: translations.interactive?.components?.retrainingTrigger?.features || [],
      benefits: translations.interactive?.components?.retrainingTrigger?.benefits || [],
      technologies: translations.interactive?.components?.retrainingTrigger?.technologies || []
    }
  ];

  // Create connection data using translations
  const mlConnections: Connection[] = [
    // Data Ingestion Phase connections
    { source: 'dataCollector', target: 'dataPreprocessor', dataType: translations.interactive?.connections?.rawData || 'Raw Data' },
    { source: 'dataPreprocessor', target: 'featureSelector', dataType: translations.interactive?.connections?.cleanedData || 'Cleaned Data' },
    { source: 'featureSelector', target: 'dataQualityMonitor', dataType: translations.interactive?.connections?.selectedFeatures || 'Selected Features' },
    { source: 'dataQualityMonitor', target: 'modelBuilder', dataType: translations.interactive?.connections?.validatedData || 'Validated Data' },

    // Model Training Phase connections
    { source: 'modelBuilder', target: 'modelTrainer', dataType: translations.interactive?.connections?.modelArchitecture || 'Model Architecture' },
    { source: 'modelTrainer', target: 'modelEvaluator', dataType: translations.interactive?.connections?.trainedModels || 'Trained Models' },
    { source: 'modelEvaluator', target: 'automaticModelSelector', dataType: translations.interactive?.connections?.evaluationMetrics || 'Evaluation Metrics' },
    { source: 'automaticModelSelector', target: 'modelDeployer', dataType: translations.interactive?.connections?.selectedModel || 'Selected Model' },

    // Deployment Phase connections
    { source: 'modelDeployer', target: 'modelPredictor', dataType: translations.interactive?.connections?.deployedModel || 'Deployed Model' },
    { source: 'modelPredictor', target: 'kubernetesCluster', dataType: translations.interactive?.connections?.predictionService || 'Prediction Service' },
    { source: 'kubernetesCluster', target: 'forecastService', dataType: translations.interactive?.connections?.infrastructure || 'Infrastructure' },
    { source: 'forecastService', target: 'predictionsMonitor', dataType: translations.interactive?.connections?.forecasts || 'Forecasts' },

    // Monitoring Phase connections
    { source: 'predictionsMonitor', target: 'alertProcessor', dataType: translations.interactive?.connections?.performanceMetrics || 'Performance Metrics' },
    { source: 'alertProcessor', target: 'notificationService', dataType: translations.interactive?.connections?.alerts || 'Alerts' },
    { source: 'notificationService', target: 'retrainingTrigger', dataType: translations.interactive?.connections?.notifications || 'Notifications' },
    { source: 'retrainingTrigger', target: 'modelTrainer', dataType: translations.interactive?.connections?.retrainingSignal || 'Retraining Signal' },

    // Cross-phase connections
    { source: 'dataQualityMonitor', target: 'alertProcessor', dataType: translations.interactive?.connections?.dataQualityAlerts || 'Data Quality Alerts' },
    { source: 'modelPredictor', target: 'predictionsMonitor', dataType: translations.interactive?.connections?.predictions || 'Predictions' },
    { source: 'retrainingTrigger', target: 'dataCollector', dataType: translations.interactive?.connections?.dataRefreshSignal || 'Data Refresh Signal' }
  ];

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  // Handle escape key for closing
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (selectedComponent) {
          setSelectedComponent(null);
        } else {
          onClose();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose, selectedComponent]);

  // Handle mouse movement for subtle parallax effect
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const rect = containerRef.current.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;
      setMousePosition({ x, y });
    };
    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
    }
    return () => {
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
      }
    };
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      containerRef.current?.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle zoom
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 2.5));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  };

  // Handle component selection
  const handleComponentClick = (componentId: string) => {
    setSelectedComponent(prev => prev === componentId ? null : componentId);
  };

  // Handle animation of data flow
  const runDataFlowAnimation = () => {
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 5000); // Animation duration
  };

  // Reset view
  const resetView = () => {
    setZoomLevel(1);
    setSelectedComponent(null);
    setViewMode('conceptual');
  };

  // Render placeholder for now - we'll implement the actual visualization in the next steps
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
        >
          <motion.div
            ref={containerRef}
            className="relative w-[90vw] h-[85vh] bg-[#121826] rounded-xl overflow-hidden border border-[#7C4DFF]/40 shadow-[0_0_30px_rgba(124,77,255,0.2)]"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header with controls */}
            <div className="absolute top-0 left-0 right-0 px-6 py-4 flex items-center justify-between z-20 bg-gradient-to-b from-[#121826] via-[#121826]/90 to-transparent backdrop-blur-sm">
              <div>
                <h2 className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500">
                  {translations.title}
                </h2>
                <p className="text-indigo-300 text-sm mt-1">
                  {translations.subtitle}
                </p>
              </div>

              <div className="flex items-center space-x-3">
                {/* View mode toggle */}
                <div className="bg-[#0A1020]/70 border border-[#7C4DFF]/40 rounded-lg overflow-hidden flex shadow-[0_0_10px_rgba(124,77,255,0.15)]">
                  <button
                    className={`px-3 py-1.5 text-sm font-medium transition-all duration-300 ${viewMode === 'conceptual' ? 'bg-[#7C4DFF]/30 text-white' : 'text-[#4FC3F7]/80 hover:text-[#4FC3F7]'}`}
                    onClick={() => setViewMode('conceptual')}
                  >
                    {translations.viewModes.conceptual}
                  </button>
                  <button
                    className={`px-3 py-1.5 text-sm font-medium transition-all duration-300 ${viewMode === 'implementation' ? 'bg-[#7C4DFF]/30 text-white' : 'text-[#4FC3F7]/80 hover:text-[#4FC3F7]'}`}
                    onClick={() => setViewMode('implementation')}
                  >
                    {translations.viewModes.implementation}
                  </button>
                </div>

                {/* Zoom controls */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={handleZoomIn}
                  aria-label={translations.buttons.zoomIn}
                >
                  <ZoomIn size={18} />
                </button>
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={handleZoomOut}
                  aria-label={translations.buttons.zoomOut}
                >
                  <ZoomOut size={18} />
                </button>

                {/* Fullscreen toggle */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-[#4FC3F7] hover:text-white border border-[#4FC3F7]/40 shadow-[0_0_10px_rgba(79,195,247,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(79,195,247,0.3)] hover:border-[#4FC3F7]/60"
                  onClick={toggleFullscreen}
                  aria-label={translations.buttons.fullscreen}
                >
                  {isFullscreen ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
                </button>

                {/* Close button */}
                <button
                  className="p-2 rounded-full bg-[#0A1020]/70 text-red-400 hover:text-red-300 border border-red-500/40 shadow-[0_0_10px_rgba(248,113,113,0.15)] transition-all duration-300 hover:shadow-[0_0_15px_rgba(248,113,113,0.3)] hover:border-red-500/60"
                  onClick={onClose}
                  aria-label={translations.buttons.close}
                >
                  <X size={18} />
                </button>
              </div>
            </div>

            {/* Interactive SVG Diagram */}
            <div
              className="absolute inset-0 overflow-hidden"
              style={{
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'center',
                transition: 'transform 0.3s ease-out'
              }}
            >
              {/* SVG for connections */}
              <svg
                ref={svgRef}
                className="absolute inset-0 w-full h-full"
                preserveAspectRatio="xMidYMid meet"
                viewBox="0 0 100 100" // Using percentages as coordinate system
              >
                <defs>
                  {/* Enhanced gradient definitions for connection lines */}
                  <linearGradient id="dataIngestionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#4FC3F7" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#4FC3F7" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="modelTrainingGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#7C4DFF" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#7C4DFF" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#7C4DFF" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="deploymentGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#00E676" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#00E676" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#00E676" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="monitoringGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#FF4081" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#FF4081" stopOpacity="0.7" />
                    <stop offset="100%" stopColor="#FF4081" stopOpacity="0.5" />
                  </linearGradient>
                  <linearGradient id="crossPhaseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9" />
                    <stop offset="50%" stopColor="#7C4DFF" stopOpacity="0.8" />
                    <stop offset="100%" stopColor="#00E676" stopOpacity="0.9" />
                  </linearGradient>

                  {/* Animated gradients */}
                  <linearGradient id="animatedDataGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#4FC3F7" stopOpacity="0.9">
                      <animate attributeName="offset" values="0;0.3;0" dur="3s" repeatCount="indefinite" />
                    </stop>
                    <stop offset="50%" stopColor="white" stopOpacity="0.9">
                      <animate attributeName="offset" values="0.3;0.6;0.3" dur="3s" repeatCount="indefinite" />
                    </stop>
                    <stop offset="100%" stopColor="#4FC3F7" stopOpacity="0.9">
                      <animate attributeName="offset" values="0.6;1;0.6" dur="3s" repeatCount="indefinite" />
                    </stop>
                  </linearGradient>

                  {/* Enhanced flow markers */}
                  <marker
                    id="flowArrow"
                    viewBox="0 0 10 10"
                    refX="5"
                    refY="5"
                    markerWidth="5"
                    markerHeight="5"
                    orient="auto-start-reverse"
                  >
                    <path d="M 0 0 L 10 5 L 0 10 z" fill="white">
                      <animate attributeName="opacity" values="0.5;1;0.5" dur="2s" repeatCount="indefinite" />
                    </path>
                  </marker>

                  {/* Enhanced glow filters for nodes */}
                  <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="2.5" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  <filter id="strongGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="4" result="blur" />
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  <filter id="pulsingGlow" x="-50%" y="-50%" width="200%" height="200%">
                    <feGaussianBlur stdDeviation="3" result="blur">
                      <animate attributeName="stdDeviation" values="2;4;2" dur="3s" repeatCount="indefinite" />
                    </feGaussianBlur>
                    <feComposite in="SourceGraphic" in2="blur" operator="over" />
                  </filter>

                  {/* Pattern for background */}
                  <pattern id="gridPattern" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#4FC3F7" strokeWidth="0.1" opacity="0.3" />
                  </pattern>
                </defs>

                {/* Background grid pattern */}
                <rect x="0" y="0" width="100" height="100" fill="url(#gridPattern)" />

                {/* Enhanced phase background areas with gradients and glows */}
                <rect
                  x="10" y="10" width="80" height="20" rx="6"
                  fill="url(#dataIngestionGradient)" fillOpacity="0.1"
                  stroke="#4FC3F7" strokeOpacity="0.5" strokeWidth="0.5"
                  filter="url(#glow)"
                />
                <rect
                  x="10" y="35" width="80" height="20" rx="6"
                  fill="url(#modelTrainingGradient)" fillOpacity="0.1"
                  stroke="#7C4DFF" strokeOpacity="0.5" strokeWidth="0.5"
                  filter="url(#glow)"
                />
                <rect
                  x="10" y="55" width="80" height="20" rx="6"
                  fill="url(#deploymentGradient)" fillOpacity="0.1"
                  stroke="#00E676" strokeOpacity="0.5" strokeWidth="0.5"
                  filter="url(#glow)"
                />
                <rect
                  x="10" y="75" width="80" height="20" rx="6"
                  fill="url(#monitoringGradient)" fillOpacity="0.1"
                  stroke="#FF4081" strokeOpacity="0.5" strokeWidth="0.5"
                  filter="url(#glow)"
                />

                {/* Phase labels */}
                <text x="12" y="15" fontSize="2" fill="#3B82F6" fontWeight="bold">{translations.phases.dataIngestion}</text>
                <text x="12" y="40" fontSize="2" fill="#8B5CF6" fontWeight="bold">{translations.phases.modelTraining}</text>
                <text x="12" y="60" fontSize="2" fill="#10B981" fontWeight="bold">{translations.phases.deployment}</text>
                <text x="12" y="80" fontSize="2" fill="#EC4899" fontWeight="bold">{translations.phases.monitoring}</text>

                {/* Render connections */}
                {mlConnections.map((conn, index) => {
                  const sourceComponent = mlComponents.find(c => c.id === conn.source);
                  const targetComponent = mlComponents.find(c => c.id === conn.target);

                  if (!sourceComponent || !targetComponent) return null;

                  // Determine connection color based on source component phase
                  let gradientId;
                  if (sourceComponent.phase === targetComponent.phase) {
                    // Same phase connections
                    switch (sourceComponent.phase) {
                      case 'dataIngestion': gradientId = 'dataIngestionGradient'; break;
                      case 'modelTraining': gradientId = 'modelTrainingGradient'; break;
                      case 'deployment': gradientId = 'deploymentGradient'; break;
                      case 'monitoring': gradientId = 'monitoringGradient'; break;
                      default: gradientId = 'crossPhaseGradient';
                    }
                  } else {
                    // Cross-phase connections
                    gradientId = 'crossPhaseGradient';
                  }

                  // Calculate path
                  const startX = sourceComponent.x;
                  const startY = sourceComponent.y;
                  const endX = targetComponent.x;
                  const endY = targetComponent.y;

                  // Create a curved path
                  const midX = (startX + endX) / 2;
                  const midY = (startY + endY) / 2;
                  const path = `M ${startX} ${startY} Q ${midX} ${midY}, ${endX} ${endY}`;

                  // Determine if this connection should be animated
                  const isAnimated = isAnimating || conn.animated;

                  return (
                    <g key={`conn-${index}`}>
                      {/* Enhanced connection line with better visibility */}
                      <path
                        d={path}
                        stroke={isAnimated ? `url(#animatedDataGradient)` : `url(#${gradientId})`}
                        strokeWidth={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "0.6" : "0.4"}
                        strokeOpacity={selectedComponent ?
                          (selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? 1 : 0.3)
                          : 0.8}
                        fill="none"
                        strokeDasharray={sourceComponent.phase !== targetComponent.phase ? "1,1" : "none"}
                        filter={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "url(#glow)" : "none"}
                        markerEnd={isAnimated ? "url(#flowArrow)" : ""}
                      />

                      {/* Connection highlight glow for selected components */}
                      {(selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id) && (
                        <path
                          d={path}
                          stroke={`url(#${gradientId})`}
                          strokeWidth="0.2"
                          strokeOpacity="0.5"
                          fill="none"
                          filter="url(#pulsingGlow)"
                        />
                      )}

                      {/* Enhanced data flow animation */}
                      {isAnimated && (
                        <>
                          <circle r="0.6" fill="white" fillOpacity="0.9" filter="url(#glow)">
                            <animateMotion
                              dur="3s"
                              repeatCount="indefinite"
                              path={path}
                            />
                          </circle>
                          <text
                            fontSize="1.6"
                            fill="white"
                            fillOpacity="0.9"
                            textAnchor="middle"
                            dominantBaseline="middle"
                            filter="url(#glow)"
                          >
                            <textPath href={`#data-path-${index}`} startOffset="50%">
                              {conn.dataType}
                            </textPath>
                          </text>
                        </>
                      )}

                      {/* Path for text labels with better visibility */}
                      <path
                        id={`data-path-${index}`}
                        d={path}
                        stroke="none"
                        fill="none"
                      />

                      {/* Always show data type label with better visibility */}
                      {!isAnimated && conn.dataType && (
                        <text
                          fontSize="1.2"
                          fill="white"
                          fillOpacity={selectedComponent === sourceComponent.id || selectedComponent === targetComponent.id ? "0.9" : "0.6"}
                          textAnchor="middle"
                          dominantBaseline="middle"
                        >
                          <textPath href={`#data-path-${index}`} startOffset="50%">
                            {conn.dataType}
                          </textPath>
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>

              {/* Component nodes */}
              {mlComponents.map((component) => {
                const isSelected = selectedComponent === component.id;

                return (
                  <Tooltip.Provider key={component.id}>
                    <Tooltip.Root delayDuration={200}>
                      <Tooltip.Trigger asChild>
                        <motion.div
                          className="absolute cursor-pointer"
                          style={{
                            left: `${component.x}%`,
                            top: `${component.y}%`,
                            width: '120px',
                            height: '120px',
                            marginLeft: '-60px', // Half of width to center
                            marginTop: '-60px',  // Half of height to center
                            zIndex: isSelected ? 30 : 20
                          }}
                          initial={{ scale: 0, opacity: 0 }}
                          animate={{
                            scale: isSelected ? 1.2 : 1,
                            opacity: 1,
                            x: isSelected ? 0 : (mousePosition.x - 50) * 0.01, // Subtle parallax effect
                            y: isSelected ? 0 : (mousePosition.y - 50) * 0.01
                          }}
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 20,
                            delay: 0.1 * mlComponents.indexOf(component)
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleComponentClick(component.id);
                          }}
                          whileHover={{ scale: 1.1 }}
                        >
                          <div
                            className="w-14 h-14 rounded-full flex items-center justify-center shadow-xl border-2 transition-all duration-300"
                            style={{
                              borderColor: isSelected ? 'white' : component.color,
                              borderWidth: isSelected ? '3px' : '2px',
                              background: `radial-gradient(circle at center, ${component.color}40, ${component.color}20)`,
                              boxShadow: isSelected
                                ? `0 0 25px ${component.color}, 0 0 15px ${component.color}`
                                : `0 0 15px ${component.color}80`,
                              transform: `scale(${isSelected ? 1.1 : 1})`,
                              filter: isSelected ? 'url(#strongGlow)' : 'url(#glow)'
                            }}
                          >
                            <span className="text-3xl" style={{ filter: 'drop-shadow(0 0 5px white)' }}>{component.icon}</span>
                          </div>
                          <div
                            className="absolute top-16 left-1/2 transform -translate-x-1/2 whitespace-nowrap transition-all duration-300"
                            style={{
                              opacity: isSelected ? 1 : 0.9,
                              transform: `translateY(${isSelected ? '2px' : '0'}) translateX(-50%) scale(${isSelected ? 1.1 : 1})`
                            }}
                          >
                            <span
                              className="text-white text-xs font-medium px-3 py-1.5 rounded-md"
                              style={{
                                background: `linear-gradient(to right, ${component.color}90, ${component.color}60)`,
                                boxShadow: `0 0 10px ${component.color}50`,
                                backdropFilter: 'blur(4px)'
                              }}
                            >
                              {component.name}
                            </span>
                          </div>
                        </motion.div>
                      </Tooltip.Trigger>
                      <Tooltip.Portal>
                        <Tooltip.Content
                          className="z-50 max-w-xs bg-[#0A1020]/95 border-2 rounded-md p-4 shadow-2xl backdrop-blur-md"
                          style={{
                            borderColor: component.color,
                            boxShadow: `0 0 20px ${component.color}50`
                          }}
                          side="top"
                          sideOffset={5}
                          align="center"
                        >
                          <div className="text-sm text-gray-300 font-open-sans">
                            <p
                              className="font-bold text-white mb-2 text-base"
                              style={{
                                color: component.color,
                                textShadow: `0 0 8px ${component.color}80`
                              }}
                            >
                              {component.name}
                            </p>
                            <p className="leading-relaxed">{component.description}</p>
                            {viewMode === 'implementation' && component.technologies && (
                              <div className="mt-3 pt-3 border-t border-gray-700">
                                <p className="text-xs text-[#4FC3F7] font-semibold mb-1">{translations.interactive?.components?.technologiesLabel || 'Technologies'}:</p>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {component.technologies.map((tech, idx) => (
                                    <span
                                      key={idx}
                                      className="text-xs px-2 py-0.5 rounded-full bg-[#7C4DFF]/20 border border-[#7C4DFF]/40"
                                    >
                                      {tech}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          <Tooltip.Arrow
                            className="fill-[#0A1020]/95"
                            style={{
                              filter: `drop-shadow(0 0 5px ${component.color}50)`
                            }}
                          />
                        </Tooltip.Content>
                      </Tooltip.Portal>
                    </Tooltip.Root>
                  </Tooltip.Provider>
                );
              })}
            </div>

            {/* Enhanced footer with additional controls */}
            <div className="absolute bottom-0 left-0 right-0 px-6 py-4 flex items-center justify-between z-20 bg-gradient-to-t from-[#121826] via-[#121826]/90 to-transparent backdrop-blur-sm">
              <button
                className="px-5 py-2.5 rounded-lg bg-gradient-to-r from-[#7C4DFF]/90 to-[#4FC3F7]/90 text-white text-sm font-medium shadow-lg shadow-[#7C4DFF]/30 hover:shadow-[#7C4DFF]/50 transition-all duration-300 hover:translate-y-[-2px] border border-white/10"
                onClick={runDataFlowAnimation}
                style={{
                  fontFamily: 'Montserrat, sans-serif',
                  boxShadow: '0 0 15px rgba(124, 77, 255, 0.3), inset 0 0 10px rgba(79, 195, 247, 0.2)'
                }}
              >
                {translations.buttons.runData}
              </button>

              <button
                className="px-4 py-2 rounded-lg bg-deep-space border border-indigo-500/30 text-indigo-300 text-sm"
                onClick={resetView}
              >
                {translations.buttons.reset}
              </button>
            </div>
          </motion.div>

          {/* Component Detail Modal */}
          <AnimatePresence>
            {selectedComponent && (
              <MLComponentDetail
                component={mlComponents.find(c => c.id === selectedComponent) || null}
                viewMode={viewMode}
                onClose={() => setSelectedComponent(null)}
                translations={{
                  keyFeatures: translations.components.keyFeatures,
                  benefits: translations.components.benefits,
                  implementationTech: translations.components.implementationTech,
                  technicalConsiderations: translations.components.technicalConsiderations,
                  technicalConsiderationsList: [translations.components.technicalConsiderations],
                  componentWorkflow: translations.components.componentWorkflow,
                  implementationArchitecture: translations.components.implementationArchitecture,
                  comingSoon: translations.components.comingSoon,
                  learnMore: translations.components.learnMore
                }}
              />
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InteractiveMLArchitecture;
