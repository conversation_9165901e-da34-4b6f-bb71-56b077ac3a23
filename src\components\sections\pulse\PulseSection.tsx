'use client';

import React, { useState, useMemo, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useRouter, useParams } from 'next/navigation';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import ParticleBackground from '@/components/animations/ParticleBackground';
import { Filter, Eye, BarChart3, Clock, Target, Layers } from 'lucide-react';

interface TimeHorizonData {
  relevanceScore: number;
  growthRate: string;
  description: string;
  predictedGrowth: string;
}

interface Technology {
  name?: string;
  description?: string;
  maturity: 'emerging' | 'growing' | 'maturing';/*  */
  category?: string;
  growthRate?: string;
  relevanceScore: number;
  industryImpact: Record<string, string>;
  businessApplications?: string[];
  predictedGrowth?: string;
  keyPlayers?: string[];
  timeHorizonData?: {
    '6months'?: TimeHorizonData;
    '1year'?: TimeHorizonData;
    '5years'?: TimeHorizonData;
  };
}

interface PulseSectionProps {
  translations?: {
    title?: string;
    subtitle?: string;
    timeHorizon?: {
      title?: string;
      options?: {
        sixMonths?: {
          label?: string;
          description?: string;
        };
        oneYear?: {
          label?: string;
          description?: string;
        };
        fiveYears?: {
          label?: string;
          description?: string;
        };
      };
    };
    maturityLevel?: {
      title?: string;
      emerging?: string;
      growing?: string;
      maturing?: string;
    };
    businessImpact?: {
      title?: string;
      relevanceScore?: string;
      highImpactFor?: string;
    };
    industries?: {
      lifeInsurance?: string;
      techServices?: string;
      manufacturing?: string;
    };
    impactLevels?: {
      high?: string;
      veryHigh?: string;
      medium?: string;
      low?: string;
    };
    contentHeader?: {
      title?: string;
      matchingFilters?: string;
    };
    labels?: {
      growth?: string;
      maturity?: string;
      relevanceScore?: string;
      explore?: string;
      businessApplications?: string;
      keyPlayers?: string;
    };
    noResults?: {
      message?: string;
      resetButton?: string;
    };
    reportCta?: {
      title?: string;
      description?: string;
      button?: string;
    };
    technologies?: {
      digitalHealthUnderwriting?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      parametricLifeProducts?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      federatedLearning?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      causalAiSystems?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      quantumSafeCryptography?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      neuralProcessAutomation?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
      digitalTwinEcosystems?: Technology & { timeHorizonData?: { '6months'?: TimeHorizonData; '1year'?: TimeHorizonData; '5years'?: TimeHorizonData; } };
    };
  };
}

const PulseSection: React.FC<PulseSectionProps> = ({ translations }) => {
  // Navigation hooks
  const router = useRouter();
  const params = useParams();
  const currentLocale = params.locale as string || 'en';

  // State management for the dashboard
  const [selectedIndustry, setSelectedIndustry] = useState<string>('lifeInsurance');
  const [timeHorizon, setTimeHorizon] = useState<string>('6months');
  const [maturityFilters, setMaturityFilters] = useState<string[]>(['emerging', 'growing', 'maturing']);
  const [selectedTech, setSelectedTech] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 });

  // Debug selectedTech state changes
  useEffect(() => {
    console.log('🔍 selectedTech state changed to:', selectedTech);
  }, [selectedTech]);

  // Define maturity level colors consistent with design system
  const maturityColors = {
    emerging: '#10b981', // green
    growing: '#f59e0b', // amber
    maturing: '#3b82f6', // blue
  };

  // Technology data from translations
  const sampleTechnologies: Technology[] = [
    {
      name: translations?.technologies?.digitalHealthUnderwriting?.name,
      description: translations?.technologies?.digitalHealthUnderwriting?.description,
      maturity: 'emerging',
      category: translations?.technologies?.digitalHealthUnderwriting?.category,
      growthRate: translations?.technologies?.digitalHealthUnderwriting?.growthRate,
      relevanceScore: 95,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: translations?.technologies?.digitalHealthUnderwriting?.businessApplications,
      predictedGrowth: translations?.technologies?.digitalHealthUnderwriting?.predictedGrowth,
      keyPlayers: translations?.technologies?.digitalHealthUnderwriting?.keyPlayers,
      timeHorizonData: translations?.technologies?.digitalHealthUnderwriting?.timeHorizonData
    },
    {
      name: translations?.technologies?.parametricLifeProducts?.name,
      description: translations?.technologies?.parametricLifeProducts?.description,
      maturity: 'growing',
      category: translations?.technologies?.parametricLifeProducts?.category,
      growthRate: translations?.technologies?.parametricLifeProducts?.growthRate,
      relevanceScore: 88,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Low' },
      businessApplications: translations?.technologies?.parametricLifeProducts?.businessApplications,
      predictedGrowth: translations?.technologies?.parametricLifeProducts?.predictedGrowth,
      keyPlayers: translations?.technologies?.parametricLifeProducts?.keyPlayers,
      timeHorizonData: translations?.technologies?.parametricLifeProducts?.timeHorizonData
    },
    {
      name: translations?.technologies?.federatedLearning?.name,
      description: translations?.technologies?.federatedLearning?.description,
      maturity: 'emerging',
      category: translations?.technologies?.federatedLearning?.category,
      growthRate: translations?.technologies?.federatedLearning?.growthRate,
      relevanceScore: 82,
      industryImpact: { lifeInsurance: 'High', techServices: 'High', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.federatedLearning?.businessApplications,
      predictedGrowth: translations?.technologies?.federatedLearning?.predictedGrowth,
      keyPlayers: translations?.technologies?.federatedLearning?.keyPlayers,
      timeHorizonData: translations?.technologies?.federatedLearning?.timeHorizonData
    },
    {
      name: translations?.technologies?.causalAiSystems?.name,
      description: translations?.technologies?.causalAiSystems?.description,
      maturity: 'emerging',
      category: translations?.technologies?.causalAiSystems?.category,
      growthRate: translations?.technologies?.causalAiSystems?.growthRate,
      relevanceScore: 79,
      industryImpact: { lifeInsurance: 'High', techServices: 'Medium', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.causalAiSystems?.businessApplications,
      predictedGrowth: translations?.technologies?.causalAiSystems?.predictedGrowth,
      keyPlayers: translations?.technologies?.causalAiSystems?.keyPlayers,
      timeHorizonData: translations?.technologies?.causalAiSystems?.timeHorizonData
    },
    {
      name: translations?.technologies?.quantumSafeCryptography?.name,
      description: translations?.technologies?.quantumSafeCryptography?.description,
      maturity: 'maturing',
      category: translations?.technologies?.quantumSafeCryptography?.category,
      growthRate: translations?.technologies?.quantumSafeCryptography?.growthRate,
      relevanceScore: 75,
      industryImpact: { lifeInsurance: 'High', techServices: 'Very High', manufacturing: 'Medium' },
      businessApplications: translations?.technologies?.quantumSafeCryptography?.businessApplications,
      predictedGrowth: translations?.technologies?.quantumSafeCryptography?.predictedGrowth,
      keyPlayers: translations?.technologies?.quantumSafeCryptography?.keyPlayers,
      timeHorizonData: translations?.technologies?.quantumSafeCryptography?.timeHorizonData
    },
    {
      name: translations?.technologies?.neuralProcessAutomation?.name,
      description: translations?.technologies?.neuralProcessAutomation?.description,
      maturity: 'growing',
      category: translations?.technologies?.neuralProcessAutomation?.category,
      growthRate: translations?.technologies?.neuralProcessAutomation?.growthRate,
      relevanceScore: 85,
      industryImpact: { lifeInsurance: 'Very High', techServices: 'High', manufacturing: 'High' },
      businessApplications: translations?.technologies?.neuralProcessAutomation?.businessApplications,
      predictedGrowth: translations?.technologies?.neuralProcessAutomation?.predictedGrowth,
      keyPlayers: translations?.technologies?.neuralProcessAutomation?.keyPlayers,
      timeHorizonData: translations?.technologies?.neuralProcessAutomation?.timeHorizonData
    },
    {
      name: translations?.technologies?.digitalTwinEcosystems?.name,
      description: translations?.technologies?.digitalTwinEcosystems?.description,
      maturity: 'maturing',
      category: translations?.technologies?.digitalTwinEcosystems?.category,
      growthRate: translations?.technologies?.digitalTwinEcosystems?.growthRate,
      relevanceScore: 71,
      industryImpact: { lifeInsurance: 'Medium', techServices: 'High', manufacturing: 'Very High' },
      businessApplications: translations?.technologies?.digitalTwinEcosystems?.businessApplications,
      predictedGrowth: translations?.technologies?.digitalTwinEcosystems?.predictedGrowth,
      keyPlayers: translations?.technologies?.digitalTwinEcosystems?.keyPlayers,
      timeHorizonData: translations?.technologies?.digitalTwinEcosystems?.timeHorizonData
    }
  ];

  // Apply time horizon data to technologies
  const applyTimeHorizonData = (tech: Technology): Technology => {
    const horizonKey = timeHorizon as '6months' | '1year' | '5years';
    const horizonData = tech.timeHorizonData?.[horizonKey];

    if (horizonData) {
      return {
        ...tech,
        relevanceScore: horizonData.relevanceScore,
        growthRate: horizonData.growthRate,
        description: horizonData.description,
        predictedGrowth: horizonData.predictedGrowth
      };
    }

    return tech;
  };

  // Get all technologies with time horizon data applied (for counts and filtering)
  const getTechnologiesWithTimeHorizonData = useMemo(() => {
    const processedTechs = sampleTechnologies.map(applyTimeHorizonData);
    console.log('🔍 Processed technologies:', processedTechs.map(t => ({ name: t.name, hasName: !!t.name })));
    return processedTechs;
  }, [timeHorizon, translations]); // Re-calculate when timeHorizon or translations change

  // Calculate maturity level counts dynamically based on time horizon and current filters
  const getMaturityCounts = useMemo(() => {
    const counts = {
      emerging: 0,
      growing: 0,
      maturing: 0
    };

    // Define relevance thresholds based on time horizon to make the effect more visible
    const relevanceThreshold = timeHorizon === '6months' ? 70 : timeHorizon === '1year' ? 60 : 50;

    // Count all technologies with time horizon data applied, but respect industry filter and relevance threshold
    const technologiesForCounting = getTechnologiesWithTimeHorizonData.filter(tech => {
      // Apply same industry filter as main filtering
      const highImpact = translations?.impactLevels?.high;
      const veryHighImpact = translations?.impactLevels?.veryHigh;
      const industryMatch = !selectedIndustry ||
        tech.industryImpact[selectedIndustry] === highImpact ||
        tech.industryImpact[selectedIndustry] === veryHighImpact;

      // Apply relevance threshold based on time horizon
      const relevanceMatch = tech.relevanceScore >= relevanceThreshold;

      return industryMatch && relevanceMatch;
    });

    technologiesForCounting.forEach(tech => {
      if (counts.hasOwnProperty(tech.maturity)) {
        counts[tech.maturity as keyof typeof counts]++;
      }
    });

    return counts;
  }, [getTechnologiesWithTimeHorizonData, selectedIndustry, translations, timeHorizon]);

  // Filter technologies based on current selections
  const getFilteredTechnologies = useMemo(() => {
    // Define relevance thresholds based on time horizon to make the effect more visible
    const relevanceThreshold = timeHorizon === '6months' ? 70 : timeHorizon === '1year' ? 60 : 50;

    return getTechnologiesWithTimeHorizonData
      .filter(tech => {
        // Industry filter
        const highImpact = translations?.impactLevels?.high;
        const veryHighImpact = translations?.impactLevels?.veryHigh;
        const industryMatch = !selectedIndustry ||
          tech.industryImpact[selectedIndustry] === highImpact ||
          tech.industryImpact[selectedIndustry] === veryHighImpact;

        // Maturity filter
        const maturityMatch = maturityFilters.includes(tech.maturity);

        // Relevance threshold based on time horizon
        const relevanceMatch = tech.relevanceScore >= relevanceThreshold;

        return industryMatch && maturityMatch && relevanceMatch;
      })
      .sort((a, b) => b.relevanceScore - a.relevanceScore); // Sort by relevance score for the selected time horizon
  }, [getTechnologiesWithTimeHorizonData, selectedIndustry, maturityFilters, translations, timeHorizon]);

  // Calculate dynamic business impact based on filtered technologies
  const calculateBusinessImpact = useMemo(() => {
    if (getFilteredTechnologies.length === 0) return 0;

    const averageRelevance = getFilteredTechnologies.reduce((sum: number, tech: Technology) => sum + tech.relevanceScore, 0) / getFilteredTechnologies.length;
    return Math.round(averageRelevance);
  }, [getFilteredTechnologies]);

  // Handle technology selection with debugging
  const handleTechSelect = (techName: string) => {
    console.log('🔍 handleTechSelect called with:', techName);
    console.log('🔍 Current selectedTech:', selectedTech);

    const newSelectedTech = selectedTech === techName ? null : techName;
    console.log('🔍 Setting selectedTech to:', newSelectedTech);

    setSelectedTech(newSelectedTech);
  };

  // Handle maturity filter toggle
  const toggleMaturityFilter = (maturity: string) => {
    if (maturityFilters.includes(maturity)) {
      setMaturityFilters(maturityFilters.filter(m => m !== maturity));
    } else {
      setMaturityFilters([...maturityFilters, maturity]);
    }
  };

  // Industry options
  const industryOptions = [
    { key: 'lifeInsurance', label: translations?.industries?.lifeInsurance, icon: '🏥' },
    { key: 'techServices', label: translations?.industries?.techServices, icon: '💻' },
    { key: 'manufacturing', label: translations?.industries?.manufacturing, icon: '🏭' }
  ];

  // Time horizon options
  const timeHorizonOptions = [
    {
      key: '6months',
      label: translations?.timeHorizon?.options?.sixMonths?.label,
      description: translations?.timeHorizon?.options?.sixMonths?.description
    },
    {
      key: '1year',
      label: translations?.timeHorizon?.options?.oneYear?.label,
      description: translations?.timeHorizon?.options?.oneYear?.description
    },
    {
      key: '5years',
      label: translations?.timeHorizon?.options?.fiveYears?.label,
      description: translations?.timeHorizon?.options?.fiveYears?.description
    }
  ];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };



  return (
    <div className="relative min-h-screen bg-space-black" style={{
      color: 'var(--color-foreground)',
      fontFamily: 'var(--font-family-base)'
    }}>
      {/* Background particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-b from-purple-900/30 to-transparent"></div>
          <ParticleBackground
            color="#00f2fe"
            secondaryColor="#4facfe"
            density="medium"
            speed="slow"
          />
        </div>
      </div>

      {/* Header Section */}
      <section ref={ref} className="relative pt-4 pb-8" id="pulse-section" style={{ marginTop: 'var(--nav-sticky-top)' }}>
        <div className="bg-deep-space border border-purple-500/20 backdrop-blur-sm">
          <div className="container">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6 py-6">
              {/* Title and Subtitle */}
              <div className="flex-1">
                <h1 className="text-3xl md:text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r" style={{
                  backgroundImage: `linear-gradient(90deg, var(--color-primary), var(--color-secondary))`
                }}>
                  {translations?.title}
                </h1>
                <p className="text-gray-300 text-lg">
                  {translations?.subtitle}
                </p>
              </div>

              {/* Industry Filter Pills - Mobile Optimized */}
              <div className="flex flex-wrap gap-2 sm:gap-3">
                {industryOptions.map((industry) => (
                  <button
                    key={industry.key}
                    className={`px-3 sm:px-4 py-3 sm:py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-300 min-h-[44px] sm:min-h-auto flex items-center justify-center ${
                      selectedIndustry === industry.key
                        ? 'text-cyan-400 bg-cyan-400/10 border border-cyan-400/30'
                        : 'bg-gray-700/50 text-gray-300 hover:bg-gray-600/50 hover:text-white border border-transparent'
                    }`}
                    onClick={() => setSelectedIndustry(industry.key)}
                  >
                    <span className="mr-1 sm:mr-2 text-sm sm:text-base">{industry.icon}</span>
                    <span className="whitespace-nowrap">{industry.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Layout */}
        <div className="container py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left Sidebar - responsive width */}
            <motion.div
              className="w-full lg:w-[320px] flex-shrink-0"
              initial={{ opacity: 0, x: -20 }}
              animate={isIntersecting ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <div className="lg:sticky lg:top-24 space-y-6">
                {/* Time Horizon Selector */}
                <div className="cyber-panel">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2" style={{ fontFamily: 'var(--font-family-display)' }}>
                    <Clock className="w-4 h-4" style={{ color: 'var(--color-primary)' }} />
                    {translations?.timeHorizon?.title}
                  </h3>
                  <div className="flex border-b border-gray-700">
                    {timeHorizonOptions.map((option) => (
                      <motion.button
                        key={option.key}
                        className={`flex-1 px-2 sm:px-4 py-4 sm:py-3 text-xs sm:text-sm font-medium transition-all duration-300 relative whitespace-nowrap min-h-[44px] flex items-center justify-center ${
                          timeHorizon === option.key
                            ? 'text-cyan-400'
                            : 'text-gray-400 hover:text-white'
                        }`}
                        onClick={() => setTimeHorizon(option.key)}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        transition={{ duration: 0.2 }}
                      >
                        {option.label}
                        {timeHorizon === option.key && (
                          <motion.div
                            className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                            layoutId="activeTimeHorizon"
                            transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                          />
                        )}
                      </motion.button>
                    ))}
                  </div>
                </div>

                {/* Maturity Level Filters */}
                <div className="cyber-panel">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2" style={{ fontFamily: 'var(--font-family-display)' }}>
                    <Filter className="w-4 h-4" style={{ color: 'var(--color-primary)' }} />
                    {translations?.maturityLevel?.title}
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(maturityColors).map(([key, color]) => (
                      <label key={key} className="flex items-center gap-3 cursor-pointer group">
                        <input
                          type="checkbox"
                          checked={maturityFilters.includes(key)}
                          onChange={() => toggleMaturityFilter(key)}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-all ${
                          maturityFilters.includes(key)
                            ? 'border-transparent'
                            : 'border-gray-600 group-hover:border-gray-500'
                        }`} style={{ backgroundColor: maturityFilters.includes(key) ? color : 'transparent' }}>
                          {maturityFilters.includes(key) && (
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          )}
                        </div>
                        <span className="text-gray-300 text-sm group-hover:text-white transition-colors">
                          {translations?.maturityLevel?.[key as keyof typeof translations.maturityLevel]}
                        </span>
                        <span className="text-xs text-gray-500 ml-auto">
                          ({getMaturityCounts[key as keyof typeof getMaturityCounts]})
                        </span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Business Impact Meter */}
                <div className="cyber-panel">
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2" style={{ fontFamily: 'var(--font-family-display)' }}>
                    <Target className="w-4 h-4" style={{ color: 'var(--color-primary)' }} />
                    {translations?.businessImpact?.title}
                  </h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-300">{translations?.businessImpact?.relevanceScore}</span>
                      <span className="text-lg font-bold" style={{ color: 'var(--color-primary)' }}>{calculateBusinessImpact}%</span>
                    </div>
                    <div className="w-full h-3 bg-gray-800 rounded-full overflow-hidden">
                      <div
                        className="h-full rounded-full transition-all duration-500"
                        style={{
                          width: `${calculateBusinessImpact}%`,
                          background: `linear-gradient(90deg, var(--color-primary), var(--color-accent))`
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-400">
                      {translations?.businessImpact?.highImpactFor} {industryOptions.find(i => i.key === selectedIndustry)?.label}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Main Content Area */}
            <motion.div
              className="flex-1"
              initial={{ opacity: 0 }}
              animate={isIntersecting ? { opacity: 1 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {/* Content Header - Mobile Optimized */}
              <div className="cyber-panel mb-6 sm:mb-8">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
                  <h2 className="text-xl sm:text-2xl font-bold text-white" style={{ fontFamily: 'var(--font-family-display)' }}>
                    {translations?.contentHeader?.title}
                  </h2>
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                    <span className="text-sm text-gray-300 order-2 sm:order-1">
                      {getFilteredTechnologies.length} {translations?.contentHeader?.matchingFilters}
                      <span className="ml-2 text-xs" style={{ color: 'var(--color-primary)' }}>({timeHorizon})</span>
                    </span>
                    <div className="flex items-center gap-2 order-1 sm:order-2">
                      <button
                        className={`p-3 sm:p-2 rounded-lg transition-all duration-300 min-h-[44px] sm:min-h-auto min-w-[44px] sm:min-w-auto flex items-center justify-center ${
                          viewMode === 'grid'
                            ? 'text-cyan-400 bg-cyan-400/10 border border-cyan-400/30'
                            : 'bg-gray-700 text-gray-400 hover:text-white hover:bg-gray-600 border border-transparent'
                        }`}
                        onClick={() => setViewMode('grid')}
                        aria-label="Grid view"
                      >
                        <Layers className="w-4 h-4" />
                      </button>
                      <button
                        className={`p-3 sm:p-2 rounded-lg transition-all duration-300 min-h-[44px] sm:min-h-auto min-w-[44px] sm:min-w-auto flex items-center justify-center ${
                          viewMode === 'list'
                            ? 'text-cyan-400 bg-cyan-400/10 border border-cyan-400/30'
                            : 'bg-gray-700 text-gray-400 hover:text-white hover:bg-gray-600 border border-transparent'
                        }`}
                        onClick={() => setViewMode('list')}
                        aria-label="List view"
                      >
                        <BarChart3 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>



              {/* Technology Cards Grid */}
              <motion.div
                key={timeHorizon} // Re-animate when time horizon changes
                variants={containerVariants}
                initial="hidden"
                animate={isIntersecting ? "visible" : "hidden"}
                className="space-y-6"
              >
                {getFilteredTechnologies.length === 0 ? (
                  <div className="cyber-panel p-12 text-center">
                    <div className="text-gray-400 mb-4 text-lg">
                      {translations?.noResults?.message}
                    </div>
                    <button
                      className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white py-3 px-6 rounded-lg font-medium transition-all duration-300 shadow-lg shadow-purple-500/20"
                      onClick={() => {
                        setMaturityFilters(['emerging', 'growing', 'maturing']);
                        setSelectedIndustry('lifeInsurance');
                      }}
                    >
                      {translations?.noResults?.resetButton}
                    </button>
                  </div>
                ) : viewMode === 'grid' ? (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                    {getFilteredTechnologies.map((tech: Technology) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className="group"
                      >
                        <div className={`cyber-panel cursor-pointer transition-all duration-300 ${
                          selectedTech === tech.name
                            ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                            : 'hover:border-gray-600'
                        }`}
                        onClick={() => {
                          console.log('🔍 Card container clicked for:', tech.name);
                          tech.name && handleTechSelect(tech.name);
                        }}
                        >
                          {/* Card Header - Mobile Optimized */}
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-3 sm:gap-4 mb-4">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <div
                                  className="w-3 h-3 sm:w-3 sm:h-3 rounded-full flex-shrink-0"
                                  style={{ backgroundColor: maturityColors[tech.maturity as keyof typeof maturityColors] }}
                                ></div>
                                <h3 className={`text-base sm:text-lg font-semibold transition-colors leading-tight ${
                                  selectedTech === tech.name ? 'text-cyan-400' : 'text-white'
                                }`} style={{ fontFamily: 'var(--font-family-display)' }}>
                                  {tech.name}
                                </h3>
                              </div>
                              <p className="text-gray-300 text-sm sm:text-sm leading-relaxed">
                                {tech.description}
                              </p>
                            </div>
                            <div className="flex sm:flex-col items-center sm:items-end gap-2 sm:gap-0 sm:text-right">
                              <div className="text-xl sm:text-2xl font-bold sm:mb-1" style={{ color: 'var(--color-primary)' }}>
                                {tech.growthRate}
                              </div>
                              <div className="text-xs text-gray-400 uppercase tracking-wide">
                                {translations?.labels?.growth}
                              </div>
                            </div>
                          </div>

                          {/* Metrics - Mobile Optimized */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4">
                            <div className="bg-gray-800/50 rounded-lg p-3 sm:p-3">
                              <div className="text-xs sm:text-xs text-gray-400 mb-2">{translations?.labels?.relevanceScore}</div>
                              <div className="flex items-center gap-2">
                                <div className="flex-1 h-2 bg-gray-700 rounded-full overflow-hidden">
                                  <div
                                    className="h-full rounded-full"
                                    style={{
                                      width: `${tech.relevanceScore}%`,
                                      background: `linear-gradient(90deg, var(--color-primary), var(--color-accent))`
                                    }}
                                  ></div>
                                </div>
                                <span className="text-sm font-medium text-white">{tech.relevanceScore}%</span>
                              </div>
                            </div>
                            <div className="bg-gray-800/50 rounded-lg p-3 sm:p-3">
                              <div className="text-xs sm:text-xs text-gray-400 mb-2">{translations?.labels?.maturity}</div>
                              <div className="text-sm font-medium text-white">
                                {translations?.maturityLevel?.[tech.maturity as keyof typeof translations.maturityLevel]}
                              </div>
                            </div>
                          </div>

                          {/* Action Button - Mobile Optimized */}
                          <div>
                            <button
                              className="w-full bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white py-3 sm:py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 shadow-lg shadow-cyan-500/20 flex items-center justify-center gap-2 min-h-[44px] sm:min-h-auto"
                              onClick={(e) => {
                                console.log('🔍 Explore button clicked for:', tech.name);
                                console.log('🔍 Event object:', e);
                                e.preventDefault();
                                e.stopPropagation(); // Prevent card click event

                                if (tech.name) {
                                  console.log('🔍 Calling handleTechSelect with:', tech.name);
                                  handleTechSelect(tech.name);
                                } else {
                                  console.warn('⚠️ tech.name is undefined or null');
                                }
                              }}
                              type="button"
                            >
                              <Eye className="w-4 h-4" />
                              {translations?.labels?.explore}
                            </button>
                          </div>

                          {/* Expanded Details */}
                          {(() => {
                            const isSelected = selectedTech === tech.name;
                            console.log(`🔍 Rendering details for ${tech.name}: selectedTech=${selectedTech}, isSelected=${isSelected}`);
                            return isSelected;
                          })() && (
                            <motion.div
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              transition={{ duration: 0.3 }}
                              className="mt-6 pt-6 border-t border-gray-700"
                            >
                              <div className="space-y-4">
                                {/* Business Applications */}
                                {tech.businessApplications && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.businessApplications}</h4>
                                    <ul className="space-y-1">
                                      {tech.businessApplications?.map((app: string, i: number) => (
                                        <li key={i} className="text-xs text-gray-300 flex items-center gap-2">
                                          <div className="w-1 h-1 bg-[#4FC3F7] rounded-full"></div>
                                          {app}
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}

                                {/* Key Players */}
                                {tech.keyPlayers && (
                                  <div>
                                    <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.keyPlayers}</h4>
                                    <div className="flex flex-wrap gap-2">
                                      {tech.keyPlayers?.map((player: string, i: number) => (
                                        <span key={i} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
                                          {player}
                                        </span>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </motion.div>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  // List View - Mobile Optimized
                  <div className="space-y-3 sm:space-y-4">
                    {getFilteredTechnologies.map((tech: Technology) => (
                      <motion.div
                        key={tech.name}
                        variants={itemVariants}
                        className={`cyber-panel transition-all duration-300 ${
                          selectedTech === tech.name
                            ? 'border-cyan-500 ring-1 ring-cyan-500/50'
                            : 'hover:border-gray-600'
                        }`}
                      >
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                          <div className="flex items-start sm:items-center gap-3 sm:gap-4 flex-1">
                            <div
                              className="w-4 h-4 rounded-full flex-shrink-0 mt-1 sm:mt-0"
                              style={{ backgroundColor: maturityColors[tech.maturity as keyof typeof maturityColors] }}
                            ></div>
                            <div className="flex-1 min-w-0">
                              <h3 className="text-base sm:text-lg font-semibold text-white leading-tight" style={{ fontFamily: 'var(--font-family-display)' }}>{tech.name}</h3>
                              <p className="text-gray-300 text-sm leading-relaxed mt-1">{tech.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end gap-4 sm:gap-6">
                            <div className="flex items-center gap-4 sm:gap-6">
                              <div className="text-center">
                                <div className="text-base sm:text-lg font-bold" style={{ color: 'var(--color-primary)' }}>{tech.growthRate}</div>
                                <div className="text-xs text-gray-400">{translations?.labels?.growth}</div>
                              </div>
                              <div className="text-center">
                                <div className="text-base sm:text-lg font-bold text-white">{tech.relevanceScore}%</div>
                                <div className="text-xs text-gray-400">{translations?.labels?.relevanceScore}</div>
                              </div>
                            </div>
                            <button
                              className="bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white py-3 sm:py-2 px-4 rounded-lg text-sm font-medium transition-all duration-300 shadow-lg shadow-cyan-500/20 min-h-[44px] sm:min-h-auto flex items-center justify-center"
                              onClick={(e) => {
                                console.log('🔍 List view Explore button clicked for:', tech.name);
                                e.preventDefault();
                                e.stopPropagation();
                                tech.name && handleTechSelect(tech.name);
                              }}
                            >
                              {translations?.labels?.explore}
                            </button>
                          </div>
                        </div>

                        {/* Expanded Details for List View */}
                        {selectedTech === tech.name && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-4 pt-4 border-t border-gray-700"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {/* Business Applications */}
                              {tech.businessApplications && (
                                <div>
                                  <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.businessApplications}</h4>
                                  <ul className="space-y-1">
                                    {tech.businessApplications?.map((app: string, i: number) => (
                                      <li key={i} className="text-xs text-gray-300 flex items-center gap-2">
                                        <div className="w-1 h-1 bg-[#4FC3F7] rounded-full"></div>
                                        {app}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {/* Key Players */}
                              {tech.keyPlayers && (
                                <div>
                                  <h4 className="text-sm font-medium text-white mb-2">{translations?.labels?.keyPlayers}</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {tech.keyPlayers?.map((player: string, i: number) => (
                                      <span key={i} className="text-xs bg-gray-700/50 text-gray-300 px-2 py-1 rounded">
                                        {player}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </motion.div>
                    ))}
                  </div>
                )}

                {/* Report Generation CTA - Mobile Optimized */}
                <div className="bg-gradient-to-r from-purple-500/10 to-cyan-500/10 border border-purple-500/30 rounded-xl p-6 sm:p-8 text-center backdrop-blur-sm">
                  <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-2" style={{ fontFamily: 'var(--font-family-display)' }}>{translations?.reportCta?.title}</h3>
                  <p className="text-gray-300 mb-6 text-sm sm:text-base leading-relaxed">
                    {translations?.reportCta?.description?.replace('{industry}', industryOptions.find(i => i.key === selectedIndustry)?.label || '')}
                  </p>
                  <motion.button
                    className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white py-4 sm:py-3 px-6 sm:px-8 rounded-lg font-medium transition-all duration-300 shadow-lg shadow-purple-500/20 min-h-[44px] sm:min-h-auto w-full sm:w-auto"
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.97 }}
                    onClick={() => {
                      router.push(`/${currentLocale}/contact`);
                    }}
                  >
                    {translations?.reportCta?.button}
                  </motion.button>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PulseSection;
