'use client';

import React, { useState, useRef, ReactNode } from 'react';
import { motion } from 'framer-motion';
import InteractiveMLArchitecture from '@/components/ui/InteractiveMLArchitecture';
import DataIngestionTab from './DataIngestionTab';
import ModelTrainingTab from './ModelTrainingTab';
import DeploymentTab from './DeploymentTab';
import MonitoringTab from './MonitoringTab';

// Component that resets scroll position when it mounts
const ScrollResetter = ({ children }: { children: ReactNode }) => {
  // This component will be remounted whenever the key changes
  // When it mounts, it will be at the top of its container
  return <div className="scroll-resetter">{children}</div>;
};

interface MLArchitecturePageContentProps {
  locale: string;
  translations: {
    title: string;
    subtitle: string;
    exploreButton: string;
    phases: {
      dataIngestion: string;
      modelTraining: string;
      deployment: string;
      monitoring: string;
    };
    viewModes: {
      conceptual: string;
      implementation: string;
    };
    buttons: {
      zoomIn: string;
      zoomOut: string;
      fullscreen: string;
      close: string;
      runData: string;
      reset: string;
    };
    architecturePhases: {
      dataIngestion: {
        title: string;
        description: string;
        icon: string;
        components: string[];
      };
      modelTraining: {
        title: string;
        description: string;
        icon: string;
        components: string[];
      };
      deployment: {
        title: string;
        description: string;
        icon: string;
        components: string[];
      };
      monitoring: {
        title: string;
        description: string;
        icon: string;
        components: string[];
      };
    };
    componentDetails: Record<string, {
      description: string;
      features: string[];
      benefits: string[];
      icon: string;
      implementationTech: string[];
      technicalConsiderations: string[];
    }>;
    features: {
      title: string;
      items: Array<{
        icon: string;
        title: string;
        description: string;
      }>;
    };
    cta: {
      title: string;
      description: string;
      exploreButton: string;
      contactButton: string;
    };
    keyComponents: string;
    keyFeatures: string;
    keyBenefits: string;
    learnMore: string;
    benefits: string;
    components: string[];
    implementationTech: string;
    technicalConsiderations: string;
    componentWorkflow: string;
    implementationArchitecture: string;
    comingSoon: string;
    interactive?: {
      components?: Record<string, {
        name?: string;
        description?: string;
        icon?: string;
        features?: string[];
        benefits?: string[];
        technologies?: string[];
      }>;
    };
  };
}

export default function MLArchitecturePage({ locale, translations }: MLArchitecturePageContentProps) {
  const [isInteractiveOpen, setIsInteractiveOpen] = useState(false);
  const [activePhase, setActivePhase] = useState('dataIngestion');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const contentSectionRef = useRef<HTMLDivElement>(null);

  // Function to handle phase change
  const handlePhaseChange = (phaseId: string) => {
    setActivePhase(phaseId);
    setTimeout(() => {
      window.scrollTo({ top: 0, behavior: 'auto' });
    }, 0);
  };

  // Interactive explorer translations
  const interactiveTranslations = {
    title: translations?.title,
    subtitle: translations?.subtitle,
    phases: {
      dataIngestion: translations?.phases?.dataIngestion,
      modelTraining: translations?.phases?.modelTraining,
      deployment: translations?.phases?.deployment,
      monitoring: translations?.phases?.monitoring,
    },
    viewModes: {
      conceptual: translations?.viewModes?.conceptual,
      implementation: translations?.viewModes?.implementation,
    },
    components: {
      // Component names will be generated from componentDetails
      keyFeatures: translations?.keyFeatures,
      benefits: translations?.keyBenefits,
      implementationTech: 'Implementation Technology',
      technicalConsiderations: 'Technical Considerations',
      componentWorkflow: 'Component Workflow',
      implementationArchitecture: 'Implementation Architecture',
      comingSoon: 'Coming Soon',
      learnMore: 'Learn More',
    },
    buttons: {
      zoomIn: translations?.buttons?.zoomIn,
      zoomOut: translations?.buttons?.zoomOut,
      fullscreen: translations?.buttons?.fullscreen,
      close: translations?.buttons?.close,
      runData: translations?.buttons?.runData,
      reset: translations?.buttons?.reset,
    },
    // Add shared componentDetails from ML tabs
    componentDetails: translations?.componentDetails || {},
    interactive: {
      // Include the interactive components data from translations
      components: translations?.interactive?.components || {},
      connections: {
        rawData: 'Raw Data',
        cleanedData: 'Cleaned Data',
        selectedFeatures: 'Selected Features',
        validatedData: 'Validated Data',
        modelArchitecture: 'Model Architecture',
        trainedModels: 'Trained Models',
        evaluationMetrics: 'Evaluation Metrics',
        selectedModel: 'Selected Model',
        deployedModel: 'Deployed Model',
        predictionService: 'Prediction Service',
        infrastructure: 'Infrastructure',
        forecasts: 'Forecasts',
        performanceMetrics: 'Performance Metrics',
        alerts: 'Alerts',
        notifications: 'Notifications',
        retrainingSignal: 'Retraining Signal',
        dataQualityAlerts: 'Data Quality Alerts',
        predictions: 'Predictions',
        dataRefreshSignal: 'Data Refresh Signal'
      }
    }
  };

  // Architecture phases data
  const architecturePhases = {
    dataIngestion: {
      title: translations?.architecturePhases?.dataIngestion?.title,
      description: translations?.architecturePhases?.dataIngestion?.description,
      icon: translations?.architecturePhases?.dataIngestion?.icon,
      components: translations?.architecturePhases?.dataIngestion?.components
    },
    modelTraining: {
      title: translations?.architecturePhases?.modelTraining?.title,
      description: translations?.architecturePhases?.modelTraining?.description,
      icon: translations?.architecturePhases?.modelTraining?.icon,
      components: translations?.architecturePhases?.modelTraining?.components
    },
    deployment: {
      title: translations?.architecturePhases?.deployment?.title,
      description: translations?.architecturePhases?.deployment?.description,
      icon: translations?.architecturePhases?.deployment?.icon,
      components: translations?.architecturePhases?.deployment?.components
    },
    monitoring: {
      title: translations?.architecturePhases?.monitoring?.title,
      description: translations?.architecturePhases?.monitoring?.description,
      icon: translations?.architecturePhases?.monitoring?.icon,
      components: translations?.architecturePhases?.monitoring?.components
    }
  };

  // Navigation component (similar to ServicesPage)
  const MLArchitectureNavigation = () => (
    <div
      id="ml-architecture-navigation"
      className="sticky w-full left-0 right-0 bg-black/90 backdrop-blur-lg py-3 border-b border-gray-800"
      style={{
        position: 'sticky',
        top: 'var(--nav-sticky-top)',
        zIndex: 'var(--service-nav-z-index)'
      }}
    >
      <div className="container mx-auto px-4">
        {/* Mobile dropdown for smaller screens */}
        <div className="block md:hidden">
          <div
            className="flex items-center justify-between py-2"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <span className="text-cyan-400 font-medium">
              {Object.entries(architecturePhases).find(([phase]) => phase === activePhase)?.[1]?.title || 'Select Phase'}
            </span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`h-5 w-5 text-cyan-400 transition-transform ${isMobileMenuOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>

          {/* Mobile dropdown menu */}
          {isMobileMenuOpen && (
            <div className="absolute left-0 right-0 mt-1 py-2 px-4 bg-gray-900/95 backdrop-blur-lg shadow-lg z-50">
              {Object.entries(architecturePhases).map(([phase, content]) => (
                <div
                  key={phase}
                  className="relative"
                  onClick={() => {
                    handlePhaseChange(phase);
                    setIsMobileMenuOpen(false);
                  }}
                >
                  <div
                    className={`px-4 py-3 text-base font-medium transition-all duration-300 cursor-pointer ${
                      activePhase === phase
                        ? 'text-cyan-400'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {content.title}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop navigation */}
        <div className="hidden md:block">
          <div className="flex flex-wrap justify-center gap-2 md:gap-8">
            {Object.entries(architecturePhases).map(([phase, content]) => (
              <div
                key={phase}
                className="relative"
              >
                <div
                  onClick={() => {
                    console.log('Clicked on phase:', phase);
                    handlePhaseChange(phase);
                  }}
                  className={`px-4 py-2 text-button font-medium transition-all duration-300 cursor-pointer ${
                    activePhase === phase
                      ? 'text-cyan-400'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  {content.title}
                </div>
                {activePhase === phase && (
                  <motion.div
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-500"
                    layoutId="activePhase"
                    transition={{ type: 'spring', stiffness: 380, damping: 30 }}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderActiveTab = () => {
    const tabProps = {
      translations: {
        architecturePhases: translations.architecturePhases,
        componentDetails: translations.componentDetails,
        keyComponents: translations.keyComponents,
        keyFeatures: translations.keyFeatures,
        keyBenefits: translations.keyBenefits,
      }
    };

    switch (activePhase) {
      case 'dataIngestion':
        return <DataIngestionTab {...tabProps} />;
      case 'modelTraining':
        return <ModelTrainingTab {...tabProps} />;
      case 'deployment':
        return <DeploymentTab {...tabProps} />;
      case 'monitoring':
        return <MonitoringTab {...tabProps} />;
      default:
        return <DataIngestionTab {...tabProps} />;
    }
  };

  return (
    <>
      {/* Header section with title and description */}
      <section className="relative bg-black pt-32 pb-8 ml-architecture-page">
        <div className="container mx-auto px-4">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-center bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 to-purple-500"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            {translations.title}
          </motion.h2>

          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto text-center mb-4"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {translations.subtitle}
          </motion.p>

          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <motion.button
              className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg shadow-lg shadow-purple-500/20"
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => setIsInteractiveOpen(true)}
            >
              {translations.exploreButton}
            </motion.button>
          </motion.div>
        </div>
      </section>

      {/* Integrated navigation section */}
      <MLArchitectureNavigation />

      {/* Content section */}
      <section className="relative bg-black pb-4">
        <div className="container mx-auto px-4" style={{ zIndex: 'var(--content-z-index)' }}>
          {/* Active phase section - using key to force remount when phase changes */}
          <div id="ml-architecture-content" className="w-full pt-8" ref={contentSectionRef}>
            {/* Using key to force the component to remount when active phase changes */}
            <ScrollResetter key={activePhase}>
              {renderActiveTab()}
            </ScrollResetter>
          </div>
        </div>
      </section>

      {/* Interactive ML Architecture */}
      <InteractiveMLArchitecture
        isOpen={isInteractiveOpen}
        onClose={() => setIsInteractiveOpen(false)}
        translations={interactiveTranslations}
      />

      {/* Features Section */}
      <div className="container mx-auto px-4 mt-4">
        <h2 className="text-3xl font-bold mb-8 text-center text-white">
          {translations.features.title}
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {translations.features.items.map((feature, index) => (
            <motion.div
              key={`feature-${index}`}
              className="bg-gray-800/50 border border-purple-500/20 rounded-lg p-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-cyan-400 text-2xl mb-1">{feature.icon}</div>
              <h3 className="text-xl font-semibold mb-1 text-white">{feature.title}</h3>
              <p className="text-gray-300">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="container mx-auto px-4 mt-16 mb-16 text-center">
        <h2 className="text-3xl font-bold mb-4 text-white">
          {translations.cta.title}
        </h2>
        <p className="text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
          {translations.cta.description}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <motion.button
            className="bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white font-medium py-3 px-6 rounded-lg shadow-lg shadow-purple-500/20"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.97 }}
            onClick={() => setIsInteractiveOpen(true)}
          >
            {translations.cta.exploreButton}
          </motion.button>
          <motion.a
            href={`/${locale}/contact`}
            className="bg-transparent border border-cyan-500 text-cyan-400 font-medium py-3 px-6 rounded-lg hover:bg-cyan-900/20 transition-colors"
            whileHover={{ scale: 1.05, y: -2 }}
            whileTap={{ scale: 0.97 }}
          >
            {translations.cta.contactButton}
          </motion.a>
        </div>
      </div>
    </>
  );
}
